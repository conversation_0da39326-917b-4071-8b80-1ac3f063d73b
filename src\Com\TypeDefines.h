//*******************************************************************
// Baige Medical
// Copyright 2022
//*******************************************************************
#ifndef _TYPE_DEFINES_H_
#define _TYPE_DEFINES_H_
//*******************************************************************
//
//*******************************************************************
#ifdef __cplusplus
    typedef char CHAR;
    typedef int BOOL;
    typedef void VOID;
    typedef unsigned char UINT8;
    typedef unsigned short int UINT16;
    typedef unsigned int UINT32;
    typedef signed char INT8;
    typedef short int INT16;
    typedef int INT32;
    typedef float FLOAT;
    typedef double DOUBLE;
    typedef UINT8 BYTE;
#else
#ifndef CHAR
#define CHAR char
#endif

#ifndef BOOL
#define BOOL int
#endif

#ifndef VOID
#define VOID void
#endif

#ifndef UINT8
#define UINT8 unsigned char
#endif

#ifndef UINT16
#define UINT16 unsigned short int
#endif

#ifndef UINT32
#define UINT32 unsigned int
#endif

#ifndef INT8
#define INT8 signed char
#endif

#ifndef INT16
#define INT16 short int
#endif

#ifndef INT32
#define INT32 int
#endif

#ifndef BYTE
#define BYTE UINT8
#endif

#ifndef WORD
#define WORD UINT16
#endif

#ifndef DWORD
#define DWORD INT32
#endif

#ifndef FLOAT
#define FLOAT float
#endif

#ifndef DOUBLE
#define DOUBLE double
#endif
#endif


#ifndef STATIC
#define STATIC static
#endif

#ifndef STRUCT
#define STRUCT struct
#endif

#ifndef CONST
#define CONST const
#endif

#ifndef VOLATILE
#define VOLATILE volatile
#endif

//*******************************************************************
//
//*******************************************************************

#ifndef FALSE
#define FALSE (0)
#endif

#ifndef TRUE
#define TRUE (1)
#endif

#ifndef NULL
#define NULL (0)
#endif
/********************************************************************
 * Bit op
 *******************************************************************/
//#define BIT(n)                (1U<<(n))

#define DOUBLE_INVALID_VALUE                                  0xFFFFFFFFFFFFFFFF//定义无效的数据
#define INT_INVALID_VALUE                                  0xFFFFFFFF//定义无效的数据
#define SHORT_INVALID_VALUE                                -32768
#define U_SHORT_INVALID_VALUE     0XFFFF
#define BYTE_INVALID_VALUE        0XFF

#define MAX_STR_SIZE 256

#define BCLR(x, n)        ( (x)& (~BIT(n)))
#define BSET(x, n)        ( (x)| BIT(n))
#define BGET(x, n)        (((x)& BIT(n))>>(n))

#define LBYTE(x)        ( (x)&0xFF)
#define HBYTE(x)        (((x)&0xFF00)>>8)

#define LWORD(x)        ( (x)&0xFFFF)
#define HWORD(x)        (((x)&0xFFFF0000)>>16)

#define MAKE_WORD(h, l)                ((((h)&0xFF)<<8)   |((l)&0xFF))
#define MAKE_DWORD(h, l)        ((((h)&0xFFFF)<<16)|((l)&0xFFFF))


/********************************************************************
 * End of File
 *******************************************************************/
#endif // _TYPE_DEFINES_H_
