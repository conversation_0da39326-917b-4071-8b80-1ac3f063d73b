#include "Spo2Module.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <stdio.h>

#ifdef __cplusplus
#include <QDateTime>
#else
#include <gd32f30x_rtc.h>
#endif

#define SPO2_CONNECT_TIMEOUT_MS         2000
#define SPO2_DATA_TIMEOUT_MS            30000


#define SPO2_Q12_PACKET_MAX_LEN         20
#define SPO2_Q12_VERSION_PACKET_LEN     3
#define SPO2_Q12_WAVE_DATA_COUNT        10
#define SPO2_Q12_HIGH_BIT_MASK          0x80
#define SPO2_Q12_LOW_7BIT_MASK          0x7F
#define SPO2_Q12_PACKET_HEADER          0x01
#define SPO2_Q12_SIGNAL_QUALITY_BAD     2
#define SPO2_Q12_CMD_VERSION            "\x83\x00\x03"
#define SPO2_Q12_CMD_VERSION_LEN        3


#define SPO2_Q15_PACKET_START_FLAG      0xA8
#define SPO2_Q15_TRANSPOSE_BYTE         0xA9
#define SPO2_Q15_TRANSPOSE_OR_MASK      0x20
#define SPO2_Q15_PACKET_MIN_LEN         7
#define SPO2_Q15_VERSION_MIN_LEN        6
#define SPO2_Q15_ERROR_MIN_LEN          8
#define SPO2_Q15_WAVE_MIN_LEN           22
#define SPO2_Q15_PARAM_MIN_LEN          12
#define SPO2_Q15_MAX_WAVE_POINTS        15
#define SPO2_Q15_INVALID_VALUE          0xFF
#define SPO2_Q15_INVALID_VALUE_16       0xFFFF
#define SPO2_Q15_CMD_VERSION_LEN        7

#define SPO2_Q15_CHANNEL_VERSION        0x01
#define SPO2_Q15_ID_PROTOCOL_VERSION    0x01
#define SPO2_Q15_ID_FIRMWARE_VERSION    0x03
#define SPO2_Q15_ID_VERSION             0x04
#define SPO2_Q15_ID_SERIAL_NUMBER       0x05
#define SPO2_Q15_CHANNEL_DATA           0x10
#define SPO2_Q15_ID_ERROR               0x01
#define SPO2_Q15_ID_WAVE                0x02
#define SPO2_Q15_ID_PARAM               0x04
#define SPO2_Q15_CMD_WAKE               0x77
#define SPO2_Q15_CMD_VERSION            "\x77\xA8\x01\x84\x43\x00\xA8"
#define SPO2_Q15_CMD_PROTOCOL_VERSION   "\x77\xA8\x01\x81\x40\xC0\xA8"
#define SPO2_Q15_CMD_FIRMWARE_VERSION   "\x77\xA8\x01\x83\x81\x41\xA8"
#define SPO2_Q15_CMD_SERIAL_NUMBER      "\x77\xA8\x01\x85\x83\xC1\xA8"

#define CRC16_TABLE_ABS { \
    0x0000, 0xCC01, 0xD801, 0x1400, 0xF001, 0x3C00, 0x2800, 0xE401, \
    0xA001, 0x6C00, 0x7800, 0xB401, 0x5000, 0x9C01, 0x8801, 0x4400  \
}

STATIC STRUCT Spo2ModuleSt sSpo2Module;

STATIC BOOL IsQ12VersionPacket(CONST UINT8* packet);
STATIC BOOL CheckDataChecksum(CONST UINT8* data, UINT8 length);
STATIC UINT16 CalculateCRC16(CONST UINT8* data, UINT8 length);
STATIC UINT8 TransposeQ15Data(CONST UINT8* tempBuffer, UINT8 packetStartIndex, UINT8 packetEndIndex, UINT8* outDecodedPacket);


STATIC enum SPO2_MODULE_TYPE DecodeModuleType();

STATIC BOOL DecodeQ12Packet();
STATIC VOID DecodeQ12WaveData(CONST UINT8* packet);
STATIC VOID DecodeQ12ParameterData(CONST UINT8* packet);
STATIC VOID DecodeQ12ErrorCode(UINT8 errorByte, UINT8 signalQuality);
STATIC BOOL DecodeQ12VersionData(CONST UINT8* packet, UINT8 packetLen);

STATIC BOOL DecodeQ15Packet();
STATIC VOID DecodeQ15WaveData(CONST UINT8* packet, UINT8 packetLen);
STATIC VOID DecodeQ15ParameterData(CONST UINT8* packet, UINT8 packetLen);
STATIC VOID DecodeQ15ErrorCode(CONST UINT8* packet, UINT8 packetLen);
STATIC VOID DecodeQ15VersionData(CONST UINT8* packet, UINT8 packetLen);
STATIC VOID DecodeQ15ProtocolVersionData(CONST UINT8* packet, UINT8 packetLen);
STATIC VOID DecodeQ15FirmwareVersionData(CONST UINT8* packet, UINT8 packetLen);
STATIC VOID DecodeQ15SerialNumber(CONST UINT8* packet, UINT8 packetLen);

STATIC VOID Spo2ModuleAddQ12VersionCmd();
STATIC VOID Spo2ModuleAddQ15VersionCmd();
STATIC VOID Spo2ModuleAddQ15ProtocolVersionCmd();
STATIC VOID Spo2ModuleAddQ15FirmwareVersionCmd();
STATIC VOID Spo2ModuleAddQ15SerialNumberCmd();


STATIC UINT32 Spo2ModuleGetSystemTime();



UINT32 Spo2ModuleGetSystemTime()
{
#ifdef __cplusplus
    return QDateTime::currentMSecsSinceEpoch();
#else
    return rtc_counter_get();
#endif
}

VOID Spo2ModuleInit()
{
//    unsigned char data[2] = {0x01, 0x84};
//    UINT16 calculatedCRC = CalculateCRC16(data, 2);

    memset(&sSpo2Module.mParameterData, 0, sizeof(STRUCT Spo2ParameterDataSt));
    memset(sSpo2Module.mWaveDataSt.mWaveData, 0, sizeof(sSpo2Module.mWaveDataSt.mWaveData));
    memset(sSpo2Module.mVersionStr, 0, sizeof(sSpo2Module.mVersionStr));
    memset(sSpo2Module.mBufferArray, 0, sizeof(sSpo2Module.mBufferArray));
    memset(sSpo2Module.mCmdArray, 0, sizeof(sSpo2Module.mCmdArray));

    sSpo2Module.mMouduleType = SPO2_MODULE_UNKNOWN;
    sSpo2Module.mIsConnect = FALSE;
    sSpo2Module.mWaveDataSt.mWaveCount = 0;
    sSpo2Module.mWaveDataSt.mCurrentIndex = 0;
    sSpo2Module.mErrorByte = 0;
    sSpo2Module.mVersionLen = 0;
    sSpo2Module.mCmdLen = 0;

    sSpo2Module.mLastReceiveTime = Spo2ModuleGetSystemTime();

    BufferCreate(&sSpo2Module.mBuffer, sSpo2Module.mBufferArray, sizeof(sSpo2Module.mBufferArray));
}

BOOL Spo2ModuleReceiveData(CONST UINT8* data, UINT8 dataLen)
{   
    if (data == NULL || dataLen == 0)
    {
        return FALSE;
    }
    
    BufferAppend(&sSpo2Module.mBuffer, data, dataLen);

    if(sSpo2Module.mMouduleType == SPO2_MODULE_UNKNOWN)
    {
        enum SPO2_MODULE_TYPE prevType = sSpo2Module.mMouduleType;
        sSpo2Module.mMouduleType = DecodeModuleType();
        
        if (prevType != sSpo2Module.mMouduleType && sSpo2Module.mMouduleType != SPO2_MODULE_UNKNOWN)
        {
            if (sSpo2Module.mMouduleType == SPO2_MODULE_Q12)
            {
                Spo2ModuleAddQ12VersionCmd();
            }
            else if (sSpo2Module.mMouduleType == SPO2_MODULE_Q15)
            {
                Spo2ModuleAddQ15VersionCmd();
                Spo2ModuleAddQ15ProtocolVersionCmd();
                Spo2ModuleAddQ15FirmwareVersionCmd();
                Spo2ModuleAddQ15SerialNumberCmd();
            }
        }
    }

    
    BOOL result = FALSE;
    
    switch (sSpo2Module.mMouduleType) 
    {
        case SPO2_MODULE_Q12:
        result = DecodeQ12Packet();
            break;
        case SPO2_MODULE_Q15:
            result = DecodeQ15Packet();
            break;
        case SPO2_MODULE_UNKNOWN:
            result = FALSE;
            break;
    }

    if(sSpo2Module.mMouduleType != SPO2_MODULE_UNKNOWN)
    {
        sSpo2Module.mIsConnect = TRUE;
        sSpo2Module.mLastReceiveTime = Spo2ModuleGetSystemTime();
    }

    return result;
}

STATIC enum SPO2_MODULE_TYPE DecodeModuleType()
{
    UINT16 dataLen = BufferLength(&sSpo2Module.mBuffer);
    
    if (dataLen == 0)
    {
        return SPO2_MODULE_UNKNOWN;
    }
    
    if (DecodeQ12Packet())
    {
        return SPO2_MODULE_Q12;
    }
    
    if (DecodeQ15Packet())
    {
        return SPO2_MODULE_Q15;
    }
    
    return SPO2_MODULE_UNKNOWN;
}

STATIC BOOL DecodeQ12Packet()
{
    UINT8 tempBuffer[256];

    UINT16 bufferLen = BufferLength(&sSpo2Module.mBuffer);
    
    if (bufferLen < SPO2_Q12_VERSION_PACKET_LEN)
        return FALSE;

    UINT8 processedBytes = 0;
    BOOL dataProcessed = FALSE;

    BufferPeek(&sSpo2Module.mBuffer, tempBuffer, bufferLen);
    
    for (UINT16 i = 0; i <= bufferLen - SPO2_Q12_VERSION_PACKET_LEN; i++)
    {
        CONST UINT8* tempVersionPacket = tempBuffer + i;
        
        if (IsQ12VersionPacket(tempVersionPacket))
        {
            if (DecodeQ12VersionData(tempVersionPacket, SPO2_Q12_VERSION_PACKET_LEN))
            {
                if (i > 0)
                {
                    UINT8 discardBuffer[256];
                    BufferPop(&sSpo2Module.mBuffer, discardBuffer, i);
                }
                
                UINT8 versionBuffer[SPO2_Q12_VERSION_PACKET_LEN];
                BufferPop(&sSpo2Module.mBuffer, versionBuffer, SPO2_Q12_VERSION_PACKET_LEN);
                return TRUE;
            }
        }
    }
    
    if (bufferLen < SPO2_Q12_PACKET_MAX_LEN)
        return FALSE;
    
    while (bufferLen - processedBytes >= SPO2_Q12_PACKET_MAX_LEN)
    {
        CONST UINT8* tempDataPacket = tempBuffer + processedBytes;

        if (CheckDataChecksum(tempDataPacket, SPO2_Q12_PACKET_MAX_LEN))
        {
            DecodeQ12WaveData(tempDataPacket);

            DecodeQ12ParameterData(tempDataPacket);

            UINT8 errorByte = tempDataPacket[16] & SPO2_Q12_LOW_7BIT_MASK;
            UINT8 signalQuality = tempDataPacket[17] & 0x03;
            DecodeQ12ErrorCode(errorByte, signalQuality);

            dataProcessed = TRUE;
            processedBytes += SPO2_Q12_PACKET_MAX_LEN;
        }
        else
        {
            processedBytes++;
        }
    }

    if (dataProcessed)
    {
        UINT8 discardBuffer[256];
        BufferPop(&sSpo2Module.mBuffer, discardBuffer, processedBytes);
    }

    return dataProcessed;
}

STATIC BOOL IsQ12VersionPacket(CONST UINT8* packet)
{
    if (!(((packet[0] & SPO2_Q12_HIGH_BIT_MASK) != 0) &&
          ((packet[1] & SPO2_Q12_HIGH_BIT_MASK) == 0) &&
          ((packet[2] & SPO2_Q12_HIGH_BIT_MASK) == 0)))
    {
        return FALSE;
    }

    UINT8 checksum = (packet[0] + packet[1]) & SPO2_Q12_LOW_7BIT_MASK;
    if (checksum != (packet[2] & SPO2_Q12_LOW_7BIT_MASK))
    {
        return FALSE;
    }

    return TRUE;
}

STATIC BOOL DecodeQ12VersionData(CONST UINT8* packet, UINT8 packetLen)
{
    if (packetLen < SPO2_Q12_VERSION_PACKET_LEN)
    {
        return FALSE;
    }

    char versionStr[32];
    UINT8 dataByte = packet[1] & SPO2_Q12_LOW_7BIT_MASK;
    UINT8 majorVersion = dataByte / 10;
    UINT8 minorVersion = dataByte % 10;

    snprintf(versionStr, sizeof(versionStr), "V%d.%d", majorVersion, minorVersion);

    UINT8 versionLen = strlen(versionStr);
    if (versionLen >= sizeof(sSpo2Module.mVersionStr))
    {
        versionLen = sizeof(sSpo2Module.mVersionStr);
    }

    memcpy(sSpo2Module.mVersionStr, versionStr, versionLen);
    sSpo2Module.mVersionLen = versionLen;

    return TRUE;
}

STATIC BOOL CheckDataChecksum(CONST UINT8* data, UINT8 length)
{
    if (!((data[0] & SPO2_Q12_HIGH_BIT_MASK) && ((data[0] & SPO2_Q12_LOW_7BIT_MASK) == SPO2_Q12_PACKET_HEADER)))
    {
        return FALSE;
    }

    UINT8 checksum = 0;
    for (INT32 i = 0; i < length - 1; i++)
    {
        checksum += data[i];
    }
    checksum &= SPO2_Q12_LOW_7BIT_MASK;

    return checksum == (data[length - 1] & SPO2_Q12_LOW_7BIT_MASK);
}

STATIC VOID DecodeQ12WaveData(CONST UINT8* packet)
{
    sSpo2Module.mWaveDataSt.mWaveCount = 0;
    
    for (INT32 i = 0; i < SPO2_Q12_WAVE_DATA_COUNT; i++)
    {
        UINT8 wavePoint = packet[i + 1] & SPO2_Q12_LOW_7BIT_MASK;

        if (i < 7)
        {
            wavePoint |= ((packet[11] >> i) & 0x01) << 7;
        }
        else
        {
            wavePoint |= ((packet[12] >> (i - 7)) & 0x01) << 7;
        }

        if (i < sizeof(sSpo2Module.mWaveDataSt.mWaveData) / sizeof(sSpo2Module.mWaveDataSt.mWaveData[0]))
        {
            sSpo2Module.mWaveDataSt.mWaveData[i] = wavePoint;
            sSpo2Module.mWaveDataSt.mWaveCount++;
        }
    }
    sSpo2Module.mWaveDataSt.mCurrentIndex = 0;
}

STATIC VOID DecodeQ12ParameterData(CONST UINT8* packet)
{
    UINT8 signalStrength = packet[13] & 0x0F;
    UINT16 pulseRate = packet[14] & SPO2_Q12_LOW_7BIT_MASK;
    pulseRate = ((packet[13] & 0x60) << 1) | pulseRate;
    UINT8 spo2 = packet[15] & SPO2_Q12_LOW_7BIT_MASK;
    UINT8 perfusionIndex = packet[18] & SPO2_Q12_LOW_7BIT_MASK;

    sSpo2Module.mParameterData.mSpo2Value = spo2;
    sSpo2Module.mParameterData.mPulseRate = pulseRate;
    sSpo2Module.mParameterData.mSignalStrength = signalStrength;
    sSpo2Module.mParameterData.mPerfusionIndex = perfusionIndex;
}

STATIC VOID DecodeQ12ErrorCode(UINT8 errorByte, UINT8 signalQuality)
{   
    sSpo2Module.mErrorByte = 0;
    
    if (errorByte & 0x02)
    {
        sSpo2Module.mErrorByte |= (1 << SPO2_ERROR_PROBE_OFF);
    }
    
    if (errorByte & 0x04)
    {
        sSpo2Module.mErrorByte |= (1 << SPO2_ERROR_NO_PROBE);
    }
    
    if (signalQuality == SPO2_Q12_SIGNAL_QUALITY_BAD)
    {
        sSpo2Module.mErrorByte |= (1 << SPO2_ERROR_BAD_SIGNAL);
    }
}

STATIC BOOL DecodeQ15Packet()
{
    UINT8 tempBuffer[256];
    UINT8 processedBytes = 0;
    BOOL dataProcessed = FALSE;
    UINT16 bufferLen = BufferLength(&sSpo2Module.mBuffer);
    
    if (bufferLen < SPO2_Q15_PACKET_MIN_LEN)
        return FALSE;
    BufferPeek(&sSpo2Module.mBuffer, tempBuffer, bufferLen);
    
    while (processedBytes < bufferLen)
    {
        BOOL isNeedFindEnd = FALSE;
        BOOL foundEndFlag = FALSE;
        UINT8 beginIndex = 0xFF;
        UINT8 endIndex = 0xFF;
        
        for (UINT8 i = processedBytes; i < bufferLen; i++)
        {
            if (tempBuffer[i] == SPO2_Q15_PACKET_START_FLAG)
            {
                if (isNeedFindEnd)
               {
                   endIndex = i;
                   foundEndFlag = TRUE;
                   break;
               }
               else
               {
                   beginIndex = i;
                   isNeedFindEnd = TRUE;
                   continue;
               }
            }
        }
        
        if (!foundEndFlag || beginIndex == 0xFF)
            break;
        
        UINT8 decodedPacket[256];
        UINT8 decodedLen = TransposeQ15Data(tempBuffer, beginIndex + 1, endIndex, decodedPacket);

        UINT16 receivedCRC = (decodedPacket[decodedLen-2] << 8) | decodedPacket[decodedLen-1];
        UINT16 calculatedCRC = CalculateCRC16(decodedPacket, decodedLen - 2);
        
        if (receivedCRC != calculatedCRC)
        {
            processedBytes = beginIndex + 1;
            continue;
        }
        
        dataProcessed = TRUE;

        UINT8 messageType = decodedPacket[1];
        UINT8 DataType = decodedPacket[2];
        if (messageType == SPO2_Q15_CHANNEL_VERSION)
        {
            switch (DataType)
            {
                case SPO2_Q15_ID_PROTOCOL_VERSION:
                    DecodeQ15ProtocolVersionData(decodedPacket, decodedLen);
                    break;
                case SPO2_Q15_ID_FIRMWARE_VERSION:
                    DecodeQ15FirmwareVersionData(decodedPacket, decodedLen);
                    break;
                case SPO2_Q15_ID_VERSION:
                    DecodeQ15VersionData(decodedPacket, decodedLen);
                    break;
                case SPO2_Q15_ID_SERIAL_NUMBER:
                    DecodeQ15SerialNumber(decodedPacket, decodedLen);
                    break;
                default:
                    break;
            }
        }
        else if (messageType == SPO2_Q15_CHANNEL_DATA)
        {
            switch (DataType)
            {
                case SPO2_Q15_ID_ERROR:
                    DecodeQ15ErrorCode(decodedPacket, decodedLen);
                    break;
                case SPO2_Q15_ID_WAVE:
                    DecodeQ15WaveData(decodedPacket, decodedLen);
                    break;
                case SPO2_Q15_ID_PARAM:
                    DecodeQ15ParameterData(decodedPacket, decodedLen);
                    break;
                default:
                    break;
            }
        } 
        processedBytes = endIndex + 1;
    }
    
    if (dataProcessed)
    {
        UINT8 discardBuffer[256];
        if (processedBytes < bufferLen)
            BufferPop(&sSpo2Module.mBuffer, discardBuffer, processedBytes);
        else
            BufferPop(&sSpo2Module.mBuffer, discardBuffer, bufferLen);
    }
    
    return dataProcessed;
}

STATIC UINT8 TransposeQ15Data(CONST UINT8* tempBuffer, UINT8 packetStartIndex, UINT8 packetEndIndex, UINT8* outDecodedPacket)
{
    UINT8 decodedLen = 0;
    for (UINT8 i = packetStartIndex; i < packetEndIndex; i++)
    {
        if (tempBuffer[i] == SPO2_Q15_TRANSPOSE_BYTE && i + 1 < packetEndIndex)
        {
            outDecodedPacket[decodedLen++] = tempBuffer[i + 1] ^ SPO2_Q15_TRANSPOSE_OR_MASK;
            i++;
        }
        else
        {
            outDecodedPacket[decodedLen++] = tempBuffer[i];
        }
    }
    return decodedLen;
}

STATIC UINT16 CalculateCRC16(CONST UINT8* data, UINT8 length)
{
    STATIC CONST UINT16 sCrcTableAbs[] = CRC16_TABLE_ABS;
    
    UINT16 index;
    UINT16 crcValue = 0xFFFF;
    UINT8 currentChar;
    
    for (index = 0; index < length; index++) 
    {
        currentChar = data[index];
        crcValue = sCrcTableAbs[(currentChar ^ crcValue) & 15] ^ (crcValue >> 4);
        crcValue = sCrcTableAbs[((currentChar >> 4) ^ crcValue) & 15] ^ (crcValue >> 4);
    }
    
    return crcValue;
}

STATIC VOID DecodeQ15VersionData(CONST UINT8* packet, UINT8 packetLen)
{
    if (packetLen < SPO2_Q15_VERSION_MIN_LEN)
    {
        return;
    }

    UINT8 versionLen = packetLen - 5;

    if (versionLen >= sizeof(sSpo2Module.mVersionStr))
    {
        versionLen = sizeof(sSpo2Module.mVersionStr);
    }

    memcpy(sSpo2Module.mVersionStr, &packet[3], versionLen);
    sSpo2Module.mVersionLen = versionLen;
}

STATIC VOID DecodeQ15ProtocolVersionData(CONST UINT8* packet, UINT8 packetLen)
{
    if (packetLen < SPO2_Q15_VERSION_MIN_LEN)
    {
        return;
    }

    UINT8 versionLen = packetLen - 5;

    if (versionLen >= sizeof(sSpo2Module.mQ15ProtocolVersionStr))
    {
        versionLen = sizeof(sSpo2Module.mQ15ProtocolVersionStr);
    }

    memcpy(sSpo2Module.mQ15ProtocolVersionStr, &packet[3], versionLen);
    sSpo2Module.mQ15ProtocolVersionLen = versionLen;
}

STATIC VOID DecodeQ15FirmwareVersionData(CONST UINT8* packet, UINT8 packetLen)
{
    if (packetLen < SPO2_Q15_VERSION_MIN_LEN)
    {
        return;
    }

    UINT8 versionLen = packetLen - 5;

    if (versionLen >= sizeof(sSpo2Module.mQ15FirmwareVersionStr))
    {
        versionLen = sizeof(sSpo2Module.mQ15FirmwareVersionStr);
    }

    memcpy(sSpo2Module.mQ15FirmwareVersionStr, &packet[3], versionLen);
    sSpo2Module.mQ15FirmwareVersionLen = versionLen;
}

STATIC VOID DecodeQ15SerialNumber(CONST UINT8* packet, UINT8 packetLen)
{
    if (packetLen < SPO2_Q15_VERSION_MIN_LEN)
    {
        return;
    }

    UINT8 versionLen = packetLen - 5;

    if (versionLen >= sizeof(sSpo2Module.mQ15SerialNumber))
    {
        versionLen = sizeof(sSpo2Module.mQ15SerialNumber);
    }

    memcpy(sSpo2Module.mQ15SerialNumber, &packet[3], versionLen);
}

STATIC VOID DecodeQ15ErrorCode(CONST UINT8* packet, UINT8 packetLen)
{
    if (packetLen < SPO2_Q15_ERROR_MIN_LEN)
    {
        return;
    }

    UINT8 status0 = packet[3];
    UINT8 status1 = packet[4];

    sSpo2Module.mErrorByte = 0;
    
    if (status0 & 0x01)
    {
        sSpo2Module.mErrorByte |= (1 << SPO2_ERROR_NO_PROBE);
    }
    
    if (status1 & 0x01)
    {
        sSpo2Module.mErrorByte |= (1 << SPO2_ERROR_PROBE_OFF);
    }

    if (status1 & 0x04)
    {
        sSpo2Module.mErrorByte |= (1 << SPO2_ERROR_NO_PULSATION);
    }

    if (status1 & 0x08)
    {
        sSpo2Module.mErrorByte |= (1 << SPO2_ERROR_WEAK_PERFUSION);
    }
    
    if (status1 & 0x80)
    {
        sSpo2Module.mErrorByte |= (1 << SPO2_ERROR_BAD_SIGNAL);
    }
}

STATIC VOID DecodeQ15WaveData(CONST UINT8* packet, UINT8 packetLen)
{
    if (packetLen < SPO2_Q15_WAVE_MIN_LEN)
    {
        return;
    }
    
    sSpo2Module.mWaveDataSt.mWaveCount = 0;
    
    UINT8 dataPoints = packetLen - 7;
    if (dataPoints > SPO2_Q15_MAX_WAVE_POINTS)
    {
        dataPoints = SPO2_Q15_MAX_WAVE_POINTS;
    }
    
    for (UINT8 i = 0; i < dataPoints; i++)
    {
        if (i < sizeof(sSpo2Module.mWaveDataSt.mWaveData) / sizeof(sSpo2Module.mWaveDataSt.mWaveData[0]))
        {
            sSpo2Module.mWaveDataSt.mWaveData[i] = packet[3 + i];
            sSpo2Module.mWaveDataSt.mWaveCount++;
        }
    }
    sSpo2Module.mWaveDataSt.mCurrentIndex = 0;
}

STATIC VOID DecodeQ15ParameterData(CONST UINT8* packet, UINT8 packetLen)
{
    if (packetLen < SPO2_Q15_PARAM_MIN_LEN)
    {
        return;
    }
    
    UINT8 spo2Value = packet[3];
    UINT16 pulseRate = (packet[4] << 8) | packet[5];
    UINT16 perfusionIndex = (packet[6] << 8) | packet[7];

    if (spo2Value != SPO2_Q15_INVALID_VALUE)
    {
        sSpo2Module.mParameterData.mSpo2Value = spo2Value;
    }
    else
    {
        sSpo2Module.mParameterData.mSpo2Value = 0;
    }
    
    if (pulseRate != SPO2_Q15_INVALID_VALUE_16)
    {
        sSpo2Module.mParameterData.mPulseRate = pulseRate;
    }
    else
    {
        sSpo2Module.mParameterData.mPulseRate = 0;
    }
    
    if (perfusionIndex != SPO2_Q15_INVALID_VALUE_16)
    {
        sSpo2Module.mParameterData.mPerfusionIndex = (perfusionIndex > 255) ? 255 : (UINT8)perfusionIndex;
    }
    else
    {
        sSpo2Module.mParameterData.mPerfusionIndex = 0;
    }
}

BOOL Spo2ModuleIsConnect()
{
    return sSpo2Module.mIsConnect;
}

BOOL Spo2ModuleGetErrorStatus(UINT8* outErrorByte)
{
    if (outErrorByte == NULL)
    {
        return FALSE;
    }
    
    *outErrorByte = sSpo2Module.mErrorByte;
    
    return TRUE;
}

BOOL Spo2ModuleGetParameterData(STRUCT Spo2ParameterDataSt* outData)
{   
    if (outData == NULL)
    {
        return FALSE;
    }
    
    memcpy(outData, &sSpo2Module.mParameterData, sizeof(STRUCT Spo2ParameterDataSt));
    
    return TRUE;
}

BOOL Spo2ModuleGetWaveData(UINT8* outWaveData)
{
    if (outWaveData == NULL)
    {
        return FALSE;
    }
    
    if (sSpo2Module.mWaveDataSt.mWaveCount == 0)
    {
        *outWaveData = 0;
        return FALSE;
    }
    
    *outWaveData = sSpo2Module.mWaveDataSt.mWaveData[sSpo2Module.mWaveDataSt.mCurrentIndex];
    
    sSpo2Module.mWaveDataSt.mCurrentIndex++;
    if(sSpo2Module.mWaveDataSt.mCurrentIndex >= sSpo2Module.mWaveDataSt.mWaveCount)
    {
        *outWaveData = 0;
        return FALSE;
    }
    
    return TRUE;
}

BOOL Spo2ModuleGetVersion(CHAR* outVersionStr, UINT8* outVersionLen)
{
    if (outVersionStr == NULL || outVersionLen == NULL || sSpo2Module.mVersionLen == 0)
    {
        return FALSE;
    }
    
    memcpy(outVersionStr, sSpo2Module.mVersionStr, sSpo2Module.mVersionLen);
    *outVersionLen = sSpo2Module.mVersionLen;
    
    sSpo2Module.mVersionLen = 0;
    return TRUE;
}

BOOL Spo2ModuleGetCmd(UINT8* outCmdData, UINT8* outCmdLen)
{
    if (outCmdData == NULL || outCmdLen == NULL || sSpo2Module.mCmdLen == 0)
    {
        return FALSE;
    }
    
    memcpy(outCmdData, sSpo2Module.mCmdArray, sSpo2Module.mCmdLen);
    *outCmdLen = sSpo2Module.mCmdLen;
    
    sSpo2Module.mCmdLen = 0;
    
    return TRUE;
}

UINT8 Spo2ModuleGetModuleType()
{
    return sSpo2Module.mMouduleType;
}

VOID Spo2ModuleUpdateStatus()
{
    UINT32 currentTime = Spo2ModuleGetSystemTime();
    UINT32 elapsedTime = currentTime - sSpo2Module.mLastReceiveTime;

    if (elapsedTime > SPO2_CONNECT_TIMEOUT_MS)
    {
        sSpo2Module.mMouduleType = SPO2_MODULE_UNKNOWN;
        sSpo2Module.mIsConnect = FALSE;
        sSpo2Module.mErrorByte = 0;
    }

    if (elapsedTime > SPO2_DATA_TIMEOUT_MS)
    {
        memset(&sSpo2Module.mParameterData, 0, sizeof(STRUCT Spo2ParameterDataSt));
        memset(sSpo2Module.mWaveDataSt.mWaveData, 0, sizeof(sSpo2Module.mWaveDataSt.mWaveData));
        sSpo2Module.mWaveDataSt.mWaveCount = 0;
        sSpo2Module.mWaveDataSt.mCurrentIndex = 0;
    }
}

STATIC VOID Spo2ModuleAddQ12VersionCmd()
{
    if (sSpo2Module.mCmdLen + SPO2_Q12_CMD_VERSION_LEN <= sizeof(sSpo2Module.mCmdArray))
    {
        memcpy(sSpo2Module.mCmdArray + sSpo2Module.mCmdLen, SPO2_Q12_CMD_VERSION, SPO2_Q12_CMD_VERSION_LEN);
        sSpo2Module.mCmdLen += SPO2_Q12_CMD_VERSION_LEN;
    }
}

STATIC VOID Spo2ModuleAddQ15VersionCmd()
{
    if (sSpo2Module.mCmdLen + SPO2_Q15_CMD_VERSION_LEN <= sizeof(sSpo2Module.mCmdArray))
    {
        memcpy(sSpo2Module.mCmdArray + sSpo2Module.mCmdLen, SPO2_Q15_CMD_VERSION, SPO2_Q15_CMD_VERSION_LEN);
        sSpo2Module.mCmdLen += SPO2_Q15_CMD_VERSION_LEN;
    }
}

STATIC VOID Spo2ModuleAddQ15ProtocolVersionCmd()
{
    if (sSpo2Module.mCmdLen + SPO2_Q15_CMD_VERSION_LEN <= sizeof(sSpo2Module.mCmdArray))
    {
        memcpy(sSpo2Module.mCmdArray + sSpo2Module.mCmdLen, SPO2_Q15_CMD_PROTOCOL_VERSION, SPO2_Q15_CMD_VERSION_LEN);
        sSpo2Module.mCmdLen += SPO2_Q15_CMD_VERSION_LEN;
    }
}

STATIC VOID Spo2ModuleAddQ15FirmwareVersionCmd()
{
    if (sSpo2Module.mCmdLen + SPO2_Q15_CMD_VERSION_LEN <= sizeof(sSpo2Module.mCmdArray))
    {
        memcpy(sSpo2Module.mCmdArray + sSpo2Module.mCmdLen, SPO2_Q15_CMD_FIRMWARE_VERSION, SPO2_Q15_CMD_VERSION_LEN);
        sSpo2Module.mCmdLen += SPO2_Q15_CMD_VERSION_LEN;
    }
}

STATIC VOID Spo2ModuleAddQ15SerialNumberCmd()
{
    if (sSpo2Module.mCmdLen + SPO2_Q15_CMD_VERSION_LEN <= sizeof(sSpo2Module.mCmdArray))
    {
        memcpy(sSpo2Module.mCmdArray + sSpo2Module.mCmdLen, SPO2_Q15_CMD_SERIAL_NUMBER, SPO2_Q15_CMD_VERSION_LEN);
        sSpo2Module.mCmdLen += SPO2_Q15_CMD_VERSION_LEN;
    }
}
