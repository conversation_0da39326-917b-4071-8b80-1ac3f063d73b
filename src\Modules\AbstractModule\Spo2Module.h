#ifndef SPO2_ABSTRACT_MODULE_H
#define SPO2_ABSTRACT_MODULE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "TypeDefines.h"
#include "Buffer.h"

enum SPO2_MODULE_TYPE
{
    SPO2_MODULE_UNKNOWN = 0,
    SPO2_MODULE_Q12,
    SPO2_MODULE_Q15
};

enum SPO2_ERROR_BIT
{
    SPO2_ERROR_PROBE_OFF,
    SPO2_ERROR_NO_PROBE,
    SPO2_ERROR_NO_PULSATION,
    SPO2_ERROR_BAD_SIGNAL,
    SPO2_ERROR_WEAK_PERFUSION,
};

STRUCT Spo2WaveDataSt
{
    UINT8 mWaveData[15];
    UINT8 mWaveCount;
    UINT8 mCurrentIndex;
};

STRUCT Spo2ParameterDataSt
{
    UINT8   mSpo2Value;
    UINT8   mSignalStrength;
    UINT16  mPulseRate;
    UINT8   mPerfusionIndex;
};

STRUCT Spo2ModuleSt
{
    enum SPO2_MODULE_TYPE mMouduleType;
    BOOL mIsConnect;
    UINT8 mErrorByte;
    UINT32 mLastReceiveTime;

    STRUCT Spo2ParameterDataSt mParameterData;
    STRUCT Spo2WaveDataSt mWaveDataSt;
    CHAR mVersionStr[32];
    UINT8 mVersionLen;
    CHAR mQ15ProtocolVersionStr[32];
    UINT8 mQ15ProtocolVersionLen;
    CHAR mQ15FirmwareVersionStr[32];
    UINT8 mQ15FirmwareVersionLen;
    CHAR mQ15SerialNumber[10];

    Buffer mBuffer;
    UINT8 mBufferArray[256];
    
    UINT8 mCmdArray[40];
    UINT8 mCmdLen;
};

VOID Spo2ModuleInit();

BOOL Spo2ModuleReceiveData(CONST UINT8* data, UINT8 dataLen);

BOOL Spo2ModuleIsConnect();

BOOL Spo2ModuleGetErrorStatus(UINT8* outErrorByte);

BOOL Spo2ModuleGetParameterData(STRUCT Spo2ParameterDataSt* outData);

BOOL Spo2ModuleGetWaveData(UINT8* outWaveData);

BOOL Spo2ModuleGetVersion(CHAR* outVersionStr, UINT8* outVersionLen);

BOOL Spo2ModuleGetCmd(UINT8* outCmdData, UINT8* outCmdLen);

UINT8 Spo2ModuleGetModuleType();

VOID Spo2ModuleUpdateStatus();

#ifdef __cplusplus
}
#endif

#endif
