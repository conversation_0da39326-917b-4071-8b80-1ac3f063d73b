#ifndef SPO2_H
#define SPO2_H

#include <QSerialPort>
#include <QTimer>
#include "LowerProxyBase.h"
#include "SpiIODevice.h"

class Spo2 : public CommProxyBase
{
    Q_OBJECT
public:
    static Spo2 *GetInstance();

    QString GetModuleVersion();
    void CloseAllTechAlarm();
    void SetEnable(bool enable);
    void CloseSerial();
protected:
    virtual void SlotReadData();

signals:
    void SignalSignalStrength(int);
    void SignalUpdateVersion(QString);
private slots:
    void SlotQ15MouduleUpdateData();
    void SlotCheckNeedSendCmd();
    void SlotGetWaveData();

private:
    explicit Spo2(QObject *parent = NULL);
    ~Spo2();

    void ProcessModuleData();
    void AdjustWaveDataTimer();
    void InitSerial();
private:
    QTimer mQ15ModuleUpdateDataTimer;
    QTimer mCheckNeedSendCmdTimer;
    QTimer mUpdateWaveDataTimer;

    QSerialPort* mSerialPort;
    QString mModuleVersion = "---";
};

#endif // SPO2_H
