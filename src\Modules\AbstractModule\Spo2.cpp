#include "Spo2.h"
#include "DebugLogManager.h"
#include "Spo2Module.h"
#include "AlarmManager.h"
#include "DataManager.h"
#include "ModuleTestManager.h"

#ifdef WIN32
#define SERIALNAME "COM15"
#else
#define SERIALNAME "/dev/ttymxc5"
#endif

#define BAUDRATE (115200)
#define STOPBIT QSerialPort::OneStop
#define PARITY QSerialPort::NoParity

#define TIMER_Q15_UPDATE_DATA_MS 200
#define TIMER_CHECK_NEED_SEND_CMD_MS 1000
#define TIMER_Q12_UPDATE_WAVE_DATA_MS 10
#define TIMER_Q15_UPDATE_WAVE_DATA_MS 13

Spo2::Spo2(QObject *parent) : CommProxyBase(parent)
{
    mSerialPort = NULL;

    Spo2ModuleInit();
    
    connect(&mQ15ModuleUpdateDataTimer, &QTimer::timeout, this, &Spo2::SlotQ15MouduleUpdateData);
    mQ15ModuleUpdateDataTimer.setInterval(TIMER_Q15_UPDATE_DATA_MS);

    connect(&mCheckNeedSendCmdTimer, &QTimer::timeout, this, [this](){
        SlotCheckNeedSendCmd();
        Spo2ModuleUpdateStatus();

        CHAR versionStr[32];
        UINT8 versionLen = 0;
        Spo2ModuleGetVersion(versionStr, &versionLen);
        if(versionLen != 0)
        {
            mModuleVersion = QString::fromLatin1(versionStr, versionLen);
            emit SignalUpdateVersion(mModuleVersion);
            DebugLog << "SpO2 Version:" << mModuleVersion;
        }
    });
    mCheckNeedSendCmdTimer.setInterval(TIMER_CHECK_NEED_SEND_CMD_MS);
    mCheckNeedSendCmdTimer.start();
    
    connect(&mUpdateWaveDataTimer, &QTimer::timeout, this, &Spo2::SlotGetWaveData);
    mUpdateWaveDataTimer.setInterval(TIMER_Q12_UPDATE_WAVE_DATA_MS);
}

Spo2::~Spo2()
{

}

Spo2 *Spo2::GetInstance()
{
    static Spo2 mInstance;
    return &mInstance;
}

QString Spo2::GetModuleVersion()
{
    return mModuleVersion;
}

void Spo2::CloseAllTechAlarm()
{
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SENSOR_DETACH, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SENSOR_NOT_CONNECT, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_NO_PULSATION, 0);
    AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SIGNAL_TOO_POOR, 0);
    AlarmManager::GetInstance()->TriggerAlarm(PROMPT_SPO2_WEAK_PERFUSION, 0);
}

void Spo2::SetEnable(bool enable)
{
    if(enable)
        InitSerial();
    else
        CloseSerial();
}

void Spo2::CloseSerial()
{
    if(mSerialPort && mSerialPort->isOpen())
        mSerialPort->close();
}

void Spo2::SlotReadData()
{
    QByteArray recvData = mCommIODevice->readAll();
    if (!recvData.isEmpty())
    {
        bool dataProcessed = Spo2ModuleReceiveData(reinterpret_cast<const uint8_t*>(recvData.constData()), recvData.size());
        
        if (dataProcessed)
        {
            ModuleTestManager::GetInstance()->UpdateModuleState(TEST_SPO2, ABLE);

            uint8_t moduleType = Spo2ModuleGetModuleType();

            if (moduleType == SPO2_MODULE_Q15)
            {
                if(!mQ15ModuleUpdateDataTimer.isActive())
                {
                    mQ15ModuleUpdateDataTimer.start();
                }
                AdjustWaveDataTimer();
            }
            else if(moduleType == SPO2_MODULE_Q12)
            {
                mQ15ModuleUpdateDataTimer.stop();
                ProcessModuleData();
                AdjustWaveDataTimer();
            }
            else
            {
                mQ15ModuleUpdateDataTimer.stop();
                AdjustWaveDataTimer();
            }
        }
    }
}

void Spo2::SlotQ15MouduleUpdateData()
{
    ProcessModuleData();
}

void Spo2::SlotCheckNeedSendCmd()
{
    uint8_t cmdData[40] = {0};
    uint8_t cmdLen = 0;

    if (Spo2ModuleGetCmd(cmdData, &cmdLen))
    {
        if (mSerialPort && mSerialPort->isOpen())
        {
            QByteArray Data(reinterpret_cast<const char*>(cmdData), cmdLen);
            SendData(Data);
        }
        else
        {
            DebugLog << "IO端口未打开，无法发送指令";
        }
    }
}

void Spo2::SlotGetWaveData()
{
    if (!Spo2ModuleIsConnect())
    {
        if (mUpdateWaveDataTimer.isActive())
        {
            mUpdateWaveDataTimer.stop();
        }
        return;
    }
    
    uint8_t wavePoint = 0;
    if (Spo2ModuleGetWaveData(&wavePoint))
    {
        struct Spo2ParameterDataSt tempParameterData;
        Spo2ModuleGetParameterData(&tempParameterData);
        
        bool sendZeroSignalStrength = (tempParameterData.mSignalStrength == 0 && Spo2ModuleGetModuleType() == SPO2_MODULE_Q12) ||
            AlarmManager::GetInstance()->IsExist(TECHALARM_SPO2_SENSOR_DETACH) ||
            AlarmManager::GetInstance()->IsExist(TECHALARM_SPO2_SENSOR_NOT_CONNECT);
        
        int plethValue = sendZeroSignalStrength ? 0 : wavePoint;
        
        DataManager::GetInstance()->UpdateWaveData(DataManager::WAVE_PLETH, plethValue, false);
        
        emit SignalSignalStrength(sendZeroSignalStrength ? 0 : plethValue / 25);
    }
}

void Spo2::AdjustWaveDataTimer()
{
    uint8_t moduleType = Spo2ModuleGetModuleType();
    
    if (moduleType == SPO2_MODULE_UNKNOWN)
    {
        if (mUpdateWaveDataTimer.isActive())
        {
            mUpdateWaveDataTimer.stop();
        }
        return;
    }
    
    int interval = (moduleType == SPO2_MODULE_Q12) ? TIMER_Q12_UPDATE_WAVE_DATA_MS : TIMER_Q15_UPDATE_WAVE_DATA_MS;
    
    if (mUpdateWaveDataTimer.interval() != interval)
    {
        mUpdateWaveDataTimer.setInterval(interval);
    }
    
    if (!mUpdateWaveDataTimer.isActive())
    {
        mUpdateWaveDataTimer.start();
    }
}

void Spo2::InitSerial()
{
    mSerialPort = new QSerialPort;
    mSerialPort->setPortName(SERIALNAME);
    mSerialPort->setBaudRate(BAUDRATE);
    mSerialPort->setStopBits(STOPBIT);
    mSerialPort->setParity(PARITY);

    if(!SetCommIODevice(mSerialPort))
        DebugLog<<"SpO2 serial connect error";
}

void Spo2::ProcessModuleData()
{
    UINT8 tempErrorByte;
    Spo2ModuleGetErrorStatus(&tempErrorByte);
    if(tempErrorByte & (1 << SPO2_ERROR_PROBE_OFF))
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SENSOR_DETACH, 1);
    else
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SENSOR_DETACH, 0);

    if(tempErrorByte & (1 << SPO2_ERROR_NO_PROBE))
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SENSOR_NOT_CONNECT, 1);
    else
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SENSOR_NOT_CONNECT, 0);

    if(tempErrorByte & (1 << SPO2_ERROR_NO_PULSATION))
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_NO_PULSATION, 1);
    else
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_NO_PULSATION, 0);

    if(tempErrorByte & (1 << SPO2_ERROR_BAD_SIGNAL))
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SIGNAL_TOO_POOR, 1);
    else
        AlarmManager::GetInstance()->TriggerAlarm(TECHALARM_SPO2_SIGNAL_TOO_POOR, 0);


    if(tempErrorByte & (1 << SPO2_ERROR_WEAK_PERFUSION))
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_SPO2_WEAK_PERFUSION, 1);
    else
        AlarmManager::GetInstance()->TriggerAlarm(PROMPT_SPO2_WEAK_PERFUSION, 0);
    
    struct Spo2ParameterDataSt tempParameterData;
    Spo2ModuleGetParameterData(&tempParameterData);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_SPO2, tempParameterData.mSpo2Value);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_PR, tempParameterData.mPulseRate);
    DataManager::GetInstance()->SaveMonitorData(VALUEDATA_PI, tempParameterData.mPerfusionIndex);
}


