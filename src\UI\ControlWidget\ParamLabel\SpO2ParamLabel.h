#ifndef SPO2PARAMLABEL_H
#define SPO2PARAMLABEL_H

#include "ParamLabelLayoutHorizontal.h"

class Spo2ParamLabel : public ParamLabelLayoutHorizontal
{
    Q_OBJECT
public:
    explicit Spo2ParamLabel(QWidget *parent =NULL);

    void SetPiValue(QString valueStr);

    void SetPrValue(QString valueStr);
    void SetPrRange(QString min,QString max);
    void SetPrScaleColor(const QColor&);
    void SetPrLowLimitColor(const QColor&);
    void SetPrHighLimitColor(const QColor&);
    void SetPrBackGroundBlinkColor(QColor color);
    void SetPrIsOpenValueBackgroundBlink(bool isOpen);

    void SetLabelScale(int) override;
private:
    void UpdateSignalStrength(int value);
protected:
    void InitConnect();

protected:
    QVector<QLabel*> mBarValueLabel{};
    QPixmap mBarPic{};

    LabelPro *mPiName{};
    LabelPro *mPiUnitLabel{};
    LabelPro *mPiValue{};

    LabelPro *mPrName{};
    LabelPro *mPrUnitLabel{};
    LabelPro *mPrValue{};
    LabelPro *mPrHighLimitLabel{};
    LabelPro *mPrLowLimitLabel{};
    QColor    mPrBackgroundBlinkColor{};
    QColor    mPrValueColor{};
};

#endif // SPO2PARAMLABEL_H 
