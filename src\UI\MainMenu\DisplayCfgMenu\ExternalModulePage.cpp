#include "ExternalModulePage.h"
#include "SystemSettingManager.h"
#include "SystemConfigManager.h"
#include "AlarmManager.h"
#include "AlarmModeManage.h"

ExternalModulePage::ExternalModulePage(QWidget *parent) : FocusWidget(parent)
{
    InitUI();
    InitConnect();
}

void ExternalModulePage::InitUiText()
{
    mSpO2Module->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SPO2_MODULE));
    ValueStrIntraData *spo2Module = new ValueStrIntraData;
    spo2Module->SetValueAndStr({{0, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_OFF)},
                                {1, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_ON)}});
    mSpO2Module->setDataModel(spo2Module);
    mSpO2Module->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::SPO2_MODULE_SWITCH));
}

void ExternalModulePage::InitUI()
{
    mSpO2Module = new TableButton(TableButton::COMBOBOX_BUTTON, this);
    mSpO2Module->SetName(UIStrings::GetStr(ALL_STRINGS_ENUM::STR_SPO2_MODULE));
    ValueStrIntraData *spo2Module = new ValueStrIntraData;
    spo2Module->SetValueAndStr({{0, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_OFF)},
                                {1, UIStrings::GetStr(ALL_STRINGS_ENUM::STR_ON)}});
    mSpO2Module->setDataModel(spo2Module);
    mSpO2Module->setValue(SettingManager->GetIntSettingValue(SystemSettingManager::SPO2_MODULE_SWITCH));

    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(mSpO2Module);
    mainLayout->addStretch();

    if(!ConfigManager->GetConfig<int>(SystemConfigManager::SPO2_BOOL_INDEX))
        mSpO2Module->hide();

    setLayout(mainLayout);

    ManageSPO2Alarms(SettingManager->GetIntSettingValue(SystemSettingManager::SPO2_MODULE_SWITCH) == 1);
}

void ExternalModulePage::InitConnect()
{
    connect(mSpO2Module, &TableButton::SignalValueChanged, this, [this](int isOpen)
    {
        SettingManager->SetSettingValue(SystemSettingManager::SPO2_MODULE_SWITCH, isOpen);
        ManageSPO2Alarms(isOpen);
    });

    connect(AlarmModeManage::GetInstance(), &AlarmModeManage::SignalAlarmModeChanged, this, [this]()
    {
        bool isSpO2Enabled = SettingManager->GetIntSettingValue(SystemSettingManager::SPO2_MODULE_SWITCH);
        ManageSPO2Alarms(isSpO2Enabled);
    }, Qt::QueuedConnection);
}

void ExternalModulePage::ManageSPO2Alarms(bool isEnabled)
{
    QVector<E_ALARM_ID> spo2PhyAlarms =
    {
        // 生理报警
        ALARM_SPO2_HIGH, ALARM_SPO2_LOW,
        ALARM_PR_HIGH, ALARM_PR_LOW
    };
    
    QVector<E_ALARM_ID> spo2TechAlarms =
    {
        // 技术报警
        TECHALARM_SPO2_MODULE_FAILURE,
        TECHALARM_SPO2_SENSOR_DETACH,
        TECHALARM_SPO2_SENSOR_NOT_CONNECT,
        TECHALARM_SPO2_NO_PULSATION,
        TECHALARM_SPO2_SIGNAL_TOO_POOR
    };
    
    QVector<E_ALARM_ID> spo2Prompts =
    {
        // 提示
        PROMPT_SPO2_SENSOR_UNUNITED,
        PROMPT_SPO2_WEAK_PERFUSION,
        PROMPT_SPO2_DISCONNECT
    };

    AlarmManager *alarmManager = AlarmManager::GetInstance();
    AlarmModeManage *alarmModeManager = AlarmModeManage::GetInstance();
    int currentAlarmMode = alarmModeManager->GetCurAlarmModel();
    
    if (currentAlarmMode == ALARM_MODE_IN_SELF_TEST || currentAlarmMode == ALARM_MODE_IN_SHUT_DOWN)
    {
        for (E_ALARM_ID alarmId : spo2PhyAlarms)
        {
            alarmManager->DisableAnAlarmWithDelay(alarmId);
        }
        
        for (E_ALARM_ID alarmId : spo2TechAlarms)
        {
            alarmManager->DisableAnAlarmWithDelay(alarmId);
        }
        
        for (E_ALARM_ID alarmId : spo2Prompts)
        {
            alarmManager->DisableAnAlarmWithDelay(alarmId);
        }
        
        return;
    }
    
    bool disablePhyAlarms = (currentAlarmMode == ALARM_MODE_IN_STANDBY || 
                            currentAlarmMode == ALARM_MODE_IN_HLM_AND_OPEN_PHY_ALARM);
    
    for (E_ALARM_ID alarmId : spo2PhyAlarms)
    {
        if (isEnabled && !disablePhyAlarms)
        {
            alarmManager->EnableAnAlarm(alarmId);
        }
        else
        {
            alarmManager->DisableAnAlarmWithDelay(alarmId);
        }
    }
    
    for (E_ALARM_ID alarmId : spo2TechAlarms)
    {
        if (isEnabled)
        {
            alarmManager->EnableAnAlarm(alarmId);
        }
        else
        {
            alarmManager->DisableAnAlarmWithDelay(alarmId);
        }
    }
    
    for (E_ALARM_ID alarmId : spo2Prompts)
    {
        if (isEnabled)
        {
            alarmManager->EnableAnAlarm(alarmId);
        }
        else
        {
            alarmManager->DisableAnAlarmWithDelay(alarmId);
        }
    }
}
