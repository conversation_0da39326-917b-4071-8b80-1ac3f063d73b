#include "SpO2ParamLabel.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include "UiApi.h"
#include "SystemSettingManager.h"
#include "Spo2.h"
#include "DemoModules.h"

#define BAR_CHART_LEVEL 10
#define BAR_PIC_WIDTH 18
#define BAR_PIC_HEIGHT 3

Spo2ParamLabel::Spo2ParamLabel(QWidget *parent)
    :ParamLabelLayoutHorizontal(parent)
{
    //setStyleSheet("border: 1px solid red;");
    mNameLabel->SetTextFontBold(true);

    mValueLabel->setAlignment(Qt::AlignCenter);

    mBarValueLabel.resize(BAR_CHART_LEVEL);

    QVBoxLayout *barWidgetLayout = new QVBoxLayout();
    barWidgetLayout->setContentsMargins(2,0,0,0);
    barWidgetLayout->setSpacing(1);

    mBarPic = QPixmap(":/Item/BarValue.png").scaled(BAR_PIC_WIDTH, BAR_PIC_HEIGHT);
    for(int i = BAR_CHART_LEVEL - 1; i >= 0; i--)
    {
        mBarValueLabel[i] = new QLabel();
        mBarValueLabel[i]->setFixedWidth(BAR_PIC_WIDTH);
        mBarValueLabel[i]->setFixedHeight(BAR_PIC_HEIGHT);
        barWidgetLayout->addWidget(mBarValueLabel[i]);
    }

    mLayoutLeft->addLayout(barWidgetLayout);
    mLayoutLeft->addStretch();
    barWidgetLayout->setAlignment(Qt::AlignLeft);


    mPiName = new LabelPro("PI", this);
    mPiValue = new LabelPro(this);
    mPiUnitLabel = new LabelPro("%", this);

    mPrName = new LabelPro("PR", this);
    mPrValue = new LabelPro(this);
    mPrUnitLabel = new LabelPro("bpm", this);
    mPrHighLimitLabel = new LabelPro("30", this);
    mPrLowLimitLabel = new LabelPro("30", this);

    //Pr布局
    mPrName->SetTextFontBold(true);
    mPrHighLimitLabel->SetTextFontBold(true);
    mPrLowLimitLabel->SetTextFontBold(true);
    mPrName->SetTextFontColor(RGB_TO_QCOLOR(BG_RGB(70, 247, 236)));
    mPrUnitLabel->SetTextFontColor(RGB_TO_QCOLOR(BG_RGB(131,129,131)));
    mPrValue->SetTextFontColor(RGB_TO_QCOLOR(BG_RGB(70, 247, 236)));
    mPrValueColor = RGB_TO_QCOLOR(BG_RGB(70, 247, 236));

    QSize limitSize = CalculateLabelTextSize(*mPrHighLimitLabel, "XXX.");
    mPrHighLimitLabel->setFixedWidth(limitSize.width());
    mPrLowLimitLabel->setFixedWidth(limitSize.width());

    mPrName->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Fixed);
    mPrUnitLabel->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Fixed);
    mPrValue->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

    QVBoxLayout* prLayoutLeft = new QVBoxLayout;
    prLayoutLeft->setContentsMargins(0, 0, 0, 0);
    prLayoutLeft->setSpacing(0);
    prLayoutLeft->addWidget(mPrName);
    prLayoutLeft->addWidget(mPrUnitLabel);
    prLayoutLeft->addStretch();
    mPrName->setAlignment(Qt::AlignLeft);
    mPrUnitLabel->setAlignment(Qt::AlignLeft);

    QVBoxLayout* prLayoutRight = new QVBoxLayout;
    prLayoutRight->setContentsMargins(0, 0, 0, 0);
    prLayoutRight->setSpacing(0);
    prLayoutRight->addWidget(mPrHighLimitLabel);
    prLayoutRight->addStretch();
    prLayoutRight->addWidget(mPrLowLimitLabel);
    mPrHighLimitLabel->setAlignment(Qt::AlignHCenter);
    mPrLowLimitLabel->setAlignment(Qt::AlignHCenter);

    QHBoxLayout* prLayoutMain = new QHBoxLayout;
    prLayoutMain->setContentsMargins(0, 0, 0, 0);
    prLayoutMain->setSpacing(1);
    prLayoutMain->addLayout(prLayoutLeft);
    prLayoutMain->addWidget(mPrValue);
    prLayoutMain->addLayout(prLayoutRight);
    prLayoutMain->setStretchFactor(prLayoutLeft, 1);
    prLayoutMain->setStretchFactor(mPrValue, 8);
    prLayoutMain->setStretchFactor(prLayoutRight, 1);
    mPrValue->setAlignment(Qt::AlignCenter);

    //Pi布局
    mPiName->SetTextFontBold(true);
    mPiName->SetTextFontColor(RGB_TO_QCOLOR(BG_RGB(70, 247, 236)));
    mPiUnitLabel->SetTextFontColor(RGB_TO_QCOLOR(BG_RGB(131,129,131)));
    mPiValue->SetTextFontColor(RGB_TO_QCOLOR(BG_RGB(70, 247, 236)));

    mPiName->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Fixed);
    mPiUnitLabel->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Fixed);
    mPiValue->setSizePolicy(QSizePolicy::Policy::Minimum, QSizePolicy::Policy::Expanding);

    QVBoxLayout* piLayoutLeft = new QVBoxLayout;
    piLayoutLeft->setContentsMargins(0, 0, 0, 0);
    piLayoutLeft->setSpacing(0);
    piLayoutLeft->addWidget(mPiName);
    piLayoutLeft->addWidget(mPiUnitLabel);
    piLayoutLeft->addStretch();
    mPiName->setAlignment(Qt::AlignLeft);
    mPiUnitLabel->setAlignment(Qt::AlignLeft);

    QHBoxLayout* piLayoutMain = new QHBoxLayout;
    piLayoutMain->setContentsMargins(0, 0, 0, 0);
    piLayoutMain->setSpacing(1);
    piLayoutMain->addLayout(piLayoutLeft);
    piLayoutMain->addWidget(mPiValue);
    piLayoutMain->setStretchFactor(piLayoutLeft, 1);
    piLayoutMain->setStretchFactor(mPiValue, 9);
    mPiValue->setAlignment(Qt::AlignCenter);

    QVBoxLayout *rightLayout = new QVBoxLayout;
    rightLayout->setSpacing(1);
    rightLayout->addLayout(prLayoutMain);
    rightLayout->addLayout(piLayoutMain);

    mLayoutMain->addLayout(rightLayout, 1);

    for(int i=0;i<BAR_CHART_LEVEL;i++)
        mBarValueLabel[i]->clear();

    InitConnect();
}

void Spo2ParamLabel::UpdateSignalStrength(int value)
{
    static int preValue = 0;
    if(value != preValue)
    {
        for(int i=0;i<BAR_CHART_LEVEL;i++)
        {
            if(i < value)
                mBarValueLabel[i]->setPixmap(mBarPic);
            else
                mBarValueLabel[i]->clear();
        }
        preValue = value;
    }
}

void Spo2ParamLabel::SetPiValue(QString valueStr)
{
    mPiValue->setText(valueStr);
}

void Spo2ParamLabel::SetPrValue(QString valueStr)
{
    mPrValue->setText(valueStr);
}

void Spo2ParamLabel::SetPrRange(QString min, QString max)
{
    if (mPrLowLimitLabel == NULL || mPrHighLimitLabel ==NULL)
        return;

    if (min == "OFF")
        ShowLimitOffIcon(*mPrLowLimitLabel);
    else
        mPrLowLimitLabel->setText(min);

    if (max == "OFF")
        ShowLimitOffIcon(*mPrHighLimitLabel);
    else
        mPrHighLimitLabel->setText(max);

    return ;
}

void Spo2ParamLabel::SetPrScaleColor(const QColor &color)
{
    if (mPrLowLimitLabel != NULL)
    {
        mPrLowLimitLabel->SetTextFontColor(color);
    }
    if (mPrHighLimitLabel != NULL)
    {
        mPrHighLimitLabel->SetTextFontColor(color);
    }
}

void Spo2ParamLabel::SetPrLowLimitColor(const QColor &color)
{
    if (mPrLowLimitLabel != NULL)
        mPrLowLimitLabel->SetTextFontColor(color);
}

void Spo2ParamLabel::SetPrHighLimitColor(const QColor &color)
{
    if (mPrHighLimitLabel != NULL)
        mPrHighLimitLabel->SetTextFontColor(color);
}

void Spo2ParamLabel::SetPrBackGroundBlinkColor(QColor color)
{
    mPrBackgroundBlinkColor = color;
}

void Spo2ParamLabel::SetPrIsOpenValueBackgroundBlink(bool isOpen)
{
    QPalette pa(mPrValue->palette());
    if (isOpen)
    {
        pa.setColor(QPalette::Window, mPrBackgroundBlinkColor);
        if (mPrBackgroundBlinkColor == COLOR_RED)
            pa.setColor(QPalette::WindowText, COLOR_WHITE);
        else if (mPrBackgroundBlinkColor == COLOR_LIGHT_YELLOW)
            pa.setColor(QPalette::WindowText, COLOR_BLACK);
        else
            pa.setColor(QPalette::WindowText, mPrValueColor);
    }
    else
    {
        pa.setColor(QPalette::Window, Qt::transparent);
        pa.setColor(QPalette::WindowText, mPrValueColor);
    }

    mPrValue->setAutoFillBackground(true);
    mPrValue->setPalette(pa);
}

void Spo2ParamLabel::SetLabelScale(int scale)
{
    LabelScaleInfoSt st = sScaleInfoMap[scale];

    mNameLabel->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mUnitLabel->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mHighLimitLabel->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mLowLimitLabel->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mValueLabel->SetTextFontSize(st.valueFontSize);

    mPrName->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mPrValue->SetTextFontSize(st.valueFontSize * 0.7);
    mPrHighLimitLabel->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mPrLowLimitLabel->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mPrUnitLabel->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);

    mPiName->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mPiUnitLabel->SetTextFontSize(sScaleInfoMap[MIN].normalFontSize);
    mPiValue->SetTextFontSize(st.valueFontSize * 0.7);
}

void Spo2ParamLabel::InitConnect()
{
    connect(Spo2::GetInstance(), &Spo2::SignalSignalStrength, this, [this](short strength){
        UpdateSignalStrength(strength);
    }, Qt::QueuedConnection);

    connect(DemoThread::GetInstance(), &DemoThread::SignalSpO2SignalStrength, this, [this](short strength){
        UpdateSignalStrength(strength);
    }, Qt::QueuedConnection);
}
