/*ScrollBar*/
ScrollBar:vertical[state="normal"]
{
background-color: rgb(53,  53,  53)/*黑灰*/;
border : 1px solid rgb(53,  53,  53)/*黑灰*/;
width:8px; /* 减小宽度 */
border-radius: 4px; /* 调整边角弧度 */
margin-left: 1px; /* 减小边距 */
margin-right: 1px; /* 减小边距 */
padding-top:2px;
padding-bottom:2px;
}
ScrollBar:horizontal[state="normal"]
{
background-color: rgb(53,  53,  53)/*黑灰*/;
border : 1px solid rgb(53,  53,  53)/*黑灰*/;
height:8px; /* 减小高度 */
border-radius: 4px; /* 调整边角弧度 */
margin-top: 1px; /* 减小边距 */
margin-bottom: 1px; /* 减小边距 */
padding-left:2px;
padding-right:2px;
}
ScrollBar::handle:vertical[state="normal"]
{
background-color: rgb(255, 255, 255)/*白*/;
border-radius: 2px; /* 调整边角弧度 */
margin-left: 0px;
margin-right: 0px;
min-width:6px; /* 减小宽度 */
min-height:25px;
}
ScrollBar::handle:horizontal[state="normal"]
{
background-color: rgb(255, 255, 255)/*白*/;
border-radius: 2px; /* 调整边角弧度 */
margin-top: 0px;
margin-bottom: 0px;
min-width:25px;
min-height:6px; /* 减小高度 */
}
ScrollBar::add-line[state="normal"],
ScrollBar::sub-line[state="normal"],
ScrollBar::add-page[state="normal"],
ScrollBar::sub-page[state="normal"]
{
background-color: rgb(53,  53,  53)/*黑灰*/;
height:0px;
border-radius: 4px; /* 调整边角弧度 */
}

ScrollBar:vertical[state="edit"]
{
background-color: rgb(134, 134, 134)/*深灰*/;
border-radius: 4px; /* 调整边角弧度 */
width:8px; /* 减小宽度 */
margin-left: 1px; /* 减小边距 */
margin-right: 1px; /* 减小边距 */
padding-top:2px;
padding-bottom:2px;
}
ScrollBar:horizontal[state="edit"]
{
background-color: rgb(134, 134, 134)/*深灰*/;
border-radius: 4px; /* 调整边角弧度 */
height:8px; /* 减小高度 */
margin-top: 1px; /* 减小边距 */
margin-bottom: 1px; /* 减小边距 */
padding-left:2px;
padding-right:2px;
}

ScrollBar::handle:vertical[state="edit"]
{
color:rgb(255, 255, 255)/*白*/;
background-color: rgb(0,  255, 255)/*亮蓝*/;
border-radius: 2px; /* 调整边角弧度 */
margin-left: 0px;
margin-right: 0px;
min-width:6px; /* 减小宽度 */
min-height:25px;
}
ScrollBar::handle:horizontal[state="edit"]
{
background-color: rgb(0,  255, 255)/*亮蓝*/;
border-radius: 2px; /* 调整边角弧度 */
margin-left: 0px;
margin-right: 0px;
min-width:25px;
min-height:6px; /* 减小高度 */
}

ScrollBar::add-line[state="edit"],
ScrollBar::sub-line[state="edit"],
ScrollBar::add-page[state="edit"],
ScrollBar::sub-page[state="edit"]
{
background-color: rgb(134, 134, 134)/*深灰*/;
height:0px;
border-radius: 4px; /* 调整边角弧度 */
}

ScrollBar:vertical[state="focus"]
{
background-color: rgb(134, 134, 134);/*深灰*/
border: 1px solid rgb(0,  255, 255)/*亮蓝*/; /* 减小边框 */
width:8px; /* 减小宽度 */
border-radius: 4px; /* 调整边角弧度 */
margin-left: 1px; /* 减小边距 */
margin-right: 1px; /* 减小边距 */
padding-top:2px;
padding-bottom:2px;
}

ScrollBar:horizontal[state="focus"]
{
color:rgb(255, 255, 255)/*白*/;
background-color: rgb(134, 134, 134);/*深灰*/
border: 1px solid rgb(0,  255, 255)/*亮蓝*/; /* 减小边框 */
height:8px; /* 减小高度 */
border-radius: 4px; /* 调整边角弧度 */
margin-top: 1px; /* 减小边距 */
margin-bottom: 1px; /* 减小边距 */
padding-left:2px;
padding-right:2px;
}
ScrollBar::handle:vertical[state="focus"]
{
color:rgb(255, 255, 255)/*白*/;
background-color: rgb(255, 255, 255)/*白*/;
border-radius: 2px; /* 调整边角弧度 */
margin-left: 0px;
margin-right: 0px;
min-width:6px; /* 减小宽度 */
min-height:25px;
}
ScrollBar::handle:horizontal[state="focus"]
{
background-color: rgb(255, 255, 255)/*白*/;
border-radius: 2px; /* 调整边角弧度 */
margin-top: 0px;
margin-bottom: 0px;
min-width:25px;
min-height:6px; /* 减小高度 */
}
ScrollBar::add-line[state="focus"],
ScrollBar::sub-line[state="focus"],
ScrollBar::add-page[state="focus"],
ScrollBar::sub-page[state="focus"]
{
background-color: rgb(134, 134, 134);/*深灰*/
height:0px;
border-radius: 4px; /* 调整边角弧度 */
}

ScrollBar:vertical[state="disable"]
{
background-color: rgb(203, 203, 203)/*灰*/;
border : 1px solid rgb(203, 203, 203)/*灰*/;
width:8px; /* 减小宽度 */
border-radius: 4px; /* 调整边角弧度 */
margin-left: 1px; /* 减小边距 */
margin-right: 1px; /* 减小边距 */
padding-top:2px;
padding-bottom:2px;
}
ScrollBar:horizontal[state="disable"]
{
background-color: rgb(203, 203, 203)/*灰*/;
border : 1px solid rgb(203, 203, 203)/*灰*/;
height:8px; /* 减小高度 */
border-radius: 4px; /* 调整边角弧度 */
margin-top: 1px; /* 减小边距 */
margin-bottom: 1px; /* 减小边距 */
padding-left:2px;
padding-right:2px;
}
ScrollBar::handle:vertical[state="disable"]
{
background-color: rgb(134, 134, 134)/*深灰*/;
border-radius: 2px; /* 调整边角弧度 */
margin-left: 0px;
margin-right: 0px;
min-width:6px; /* 减小宽度 */
min-height:25px;
}
ScrollBar::handle:horizontal[state="disable"]
{
background-color: rgb(134, 134, 134)/*深灰*/;
border-radius: 2px; /* 调整边角弧度 */
margin-top: 0px;
margin-bottom: 0px;
min-width:25px;
min-height:6px; /* 减小高度 */
}
ScrollBar::add-line[state="disable"],
ScrollBar::sub-line[state="disable"],
ScrollBar::add-page[state="disable"],
ScrollBar::sub-page[state="disable"]
{
background-color: rgb(203, 203, 203)/*灰*/;
height:0px;
border-radius: 4px; /* 调整边角弧度 */
}

/*ScrollBar*/
